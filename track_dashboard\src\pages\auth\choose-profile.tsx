import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
   Box,
   Button,
   Image,
   useColorModeValue,
   Stack,
   Heading,
   Text,
   SimpleGrid,
   Card,
   CardBody,
} from '@chakra-ui/react';
import { useApiQuery } from '../../hooks/react-query-hooks';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';

import ICON from '../../assets/icons/icon.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints, { AllUserDetails, GetAllProfilesResponse } from '../../api/service/auth';

function ChooseProfilePage() {
   const navigate = useNavigate();

   const { data: allProfiles, isLoading } = useApiQuery<GetAllProfilesResponse>({
      queryKey: ['allprofile'],
      queryFn: () => authEndpoints.fetchUserDetails({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
      }),
      selectHandler: (data) => {
         if (data.details.length === 1) {
            const userDetails: AuthUser = {
               email: data.details[0].email_address,
               client_id: data.details[0].client_id,
               company_name: data.details[0].company_name,
               company_url: data.details[0].company_url,
               organization_type: data.details[0].organization_type,
               user_role: data.details[0].user_role,
               fullName: data.details[0].full_name,
               user_id: data.details[0].user_id,
               
            };
            LocalStorageService.setItem(Keys.FlableUserDetails, userDetails);
            LocalStorageService.setItem(
               Keys.UserName,
               data.details[0].email_address,
            );
            LocalStorageService.setItem(
               Keys.ClientId,
               data.details[0].client_id,
            );
         }
         return data;
      },
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
   });

   const handleProfileSelection = (profile: AllUserDetails) => {
      const userDetails: AuthUser = {
         email: profile.email_address,
         client_id: profile.client_id,
         company_name: profile.company_name,
         company_url: profile.company_url,
         organization_type: profile.organization_type,
         fullName: profile.full_name,
         user_role: profile.user_role,
         user_id: profile.user_id,

      };

      LocalStorageService.setItem(Keys.FlableUserDetails, userDetails);
      LocalStorageService.setItem(Keys.UserName, profile.email_address);
      LocalStorageService.setItem(Keys.ClientId, profile.client_id);

      navigate('/');
   };

   if (isLoading) {
      return (
         <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
            <Stack align='center'>
               <Image src={ICON} alt='Logo' boxSize='60px' />
               <Heading fontSize='4xl' textAlign='center'>
                  Loading...
               </Heading>
            </Stack>
         </Stack>
      );
   }

   if (!allProfiles || allProfiles.details.length === 0) {
      return (
         <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
            <Stack align='center'>
               <Image src={ICON} alt='Logo' boxSize='60px' />
               <Heading fontSize='4xl' textAlign='center'>
                  No Profiles Found
               </Heading>
               <Text>Please contact support.</Text>
            </Stack>
         </Stack>
      );
   }

   if (allProfiles.details.length === 1) {
      // Auto-redirect if only one profile
      React.useEffect(() => {
         navigate('/');
      }, [navigate]);
      
      return (
         <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
            <Stack align='center'>
               <Image src={ICON} alt='Logo' boxSize='60px' />
               <Heading fontSize='4xl' textAlign='center'>
                  Redirecting...
               </Heading>
            </Stack>
         </Stack>
      );
   }

   return (
      <Stack spacing={8} mx='auto' maxW='4xl' py={12} px={6}>
         <Stack align='center'>
            <Image src={ICON} alt='Logo' boxSize='60px' />
            <Heading fontSize='4xl' textAlign='center'>
               Choose Profile
            </Heading>
            <Text fontSize='lg' color='gray.600' textAlign='center'>
               Select the profile you want to use
            </Text>
         </Stack>
         
         <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            {allProfiles?.details.filter((profile)=>profile.is_active).map((profile:AllUserDetails) => (
               <Card
                  key={profile.client_id}
                  bg={useColorModeValue('white', 'gray.700')}
                  boxShadow='lg'
                  cursor='pointer'
                  _hover={{ transform: 'translateY(-2px)', boxShadow: 'xl' }}
                  onClick={() => handleProfileSelection(profile)}
               >
                  <CardBody>
                     <Stack spacing={3}>
                        <Heading size='md'>{profile.company_name}</Heading>
                        <Text color='gray.600'>{profile.full_name}</Text>
                        <Text fontSize='sm' color='gray.500'>
                           {profile.organization_type}
                        </Text>
                        <Text fontSize='sm' color='gray.500'>
                           Role: {profile.user_role}
                        </Text>
                        <Button
                           size='sm'
                           colorScheme='blue'
                           variant='outline'
                           mt={2}
                        >
                           Select Profile
                        </Button>
                     </Stack>
                  </CardBody>
               </Card>
            ))}
         </SimpleGrid>
      </Stack>
   );
}

export default ChooseProfilePage;
