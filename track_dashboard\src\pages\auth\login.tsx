import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';
import {
   Box,
   Button,
   FormControl,
   Input,
   Image,
   useToast,
   FormErrorMessage,
   InputGroup,
   InputRightElement,
   Text,
   FormLabel,
   useColorModeValue,
   Stack,
   Heading,
   Link,
} from '@chakra-ui/react';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { loginStrings } from '../../utils/strings/login-strings';
import { appStrings } from '../../utils/strings/app-strings';
import { useAppDispatch } from '../../store/store';
import { LocalStorageService, Keys } from '../../utils/local-storage';

import ICON from '../../assets/icons/icon.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';
import { regex } from '../../utils/strings/auth-strings';

function Login() {
   const [showPassword, setShowPassword] = useState(false);
   const [form, setForm] = useState({
      email_address: '',
      password: '',
   });
   const [error, setError] = useState({
      email_address: '',
      password: '',
   });
   const navigate = useNavigate();
   const toast = useToast();
   const dispatch = useAppDispatch();

   const { isPending, mutate } = useApiMutation({
      queryKey: [keys.login],
      mutationFn: authEndpoints.login,
      onSuccessHandler: (response) => {
         if (response.message === 'OTP Verification Required.') {
            LocalStorageService.setItem(Keys.UserName, response.email_address);
            navigate('/auth/email-verification');
            toast({
               title: loginStrings.loginSuccessful,
               description: 'OTP verification pending.',
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
         } else {
            const { email_address, refreshToken } = response;

            LocalStorageService.setItem(Keys.Token, refreshToken as string);
            LocalStorageService.setItem(Keys.UserName, email_address);

            navigate('/auth/choose-profile');

            toast({
               title: loginStrings.loginSuccessful,
               description: response.message,
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
         }
      },
      onError: (msg) => {
         toast({
            title: loginStrings.loginFailed,
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateFields = (name: string, value: string) => {
      let errorMessage = '';

      switch (name) {
         case 'email_address':
            if (!value.trim()) {
               errorMessage = loginStrings.emailRequired;
            } else if (!regex.email.test(value)) {
               errorMessage = loginStrings.invalidEmail;
            }
            break;
         case 'password':
            if (!value.trim()) {
               errorMessage = loginStrings.passwordRequired;
            } else if (!regex.password.test(value)) {
               errorMessage = loginStrings.passwordMinLength;
            }
            break;
         default:
            break;
      }

      setError((prevError) => ({
         ...prevError,
         [name]: errorMessage,
      }));

      return errorMessage === '';
   };

   const validateForm = () => {
      const isEmailValid = validateFields('email_address', form.email_address);
      const isPasswordValid = validateFields('password', form.password);
      return isEmailValid && isPasswordValid;
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: value,
      }));
      validateFields(name, value);
   };

   const handleNavigateToRegister = () => {
      navigate('/auth/register');
   };

   const handleSubmit = () => {
      if (!validateForm()) {
         return;
      }
      mutate(form);
   };

   return (
      <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
         <Stack align='center'>
            <Image src={ICON} alt='Logo' boxSize='60px' />
            <Heading fontSize='4xl' textAlign='center'>
               {loginStrings.login}
            </Heading>
         </Stack>
         <Box
            rounded='lg'
            bg={useColorModeValue('white', 'gray.700')}
            boxShadow='lg'
            p={8}
         >
            <Stack spacing={4}>
               <FormControl isInvalid={!!error.email_address}>
                  <FormLabel>{loginStrings.email}</FormLabel>
                  <Input
                     name='email_address'
                     type='email'
                     value={form.email_address}
                     onChange={handleChange}
                     placeholder='Enter your email'
                  />
                  <FormErrorMessage>{error.email_address}</FormErrorMessage>
               </FormControl>
               
               <FormControl isInvalid={!!error.password}>
                  <FormLabel>{loginStrings.password}</FormLabel>
                  <InputGroup>
                     <Input
                        name='password'
                        type={showPassword ? 'text' : 'password'}
                        value={form.password}
                        onChange={handleChange}
                        placeholder='Enter your password'
                     />
                     <InputRightElement>
                        <Button
                           variant='ghost'
                           onClick={() => setShowPassword(!showPassword)}
                        >
                           {showPassword ? <FaEyeSlash /> : <FaEye />}
                        </Button>
                     </InputRightElement>
                  </InputGroup>
                  <FormErrorMessage>{error.password}</FormErrorMessage>
               </FormControl>
               
               <Stack spacing={10} pt={2}>
                  <Button
                     loadingText='Signing in...'
                     size='lg'
                     bg='blue.400'
                     color='white'
                     _hover={{
                        bg: 'blue.500',
                     }}
                     onClick={handleSubmit}
                     isLoading={isPending}
                  >
                     {loginStrings.login}
                  </Button>
               </Stack>
               
               <Stack pt={6}>
                  <Text align='center'>
                     {loginStrings.dontHaveAccount}{' '}
                     <Link color='blue.400' onClick={handleNavigateToRegister}>
                        {loginStrings.signUp}
                     </Link>
                  </Text>
               </Stack>
            </Stack>
         </Box>
      </Stack>
   );
}

export default Login;
