import { defineConfig } from 'vite';
import path from 'path';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';
import svgr from 'vite-plugin-svgr';

export default defineConfig({
   build: {
      rollupOptions: {
         input: 'index.html',
      },
   },

   envDir: 'config/env',
   plugins: [react(), tailwindcss(), svgr()],
   resolve: {
      alias: {
         '@': path.resolve(__dirname, './src'),
      },
   },
   server: {
      port: 3001, // Different port from new_dashboard
      hmr: true,
   },
});
