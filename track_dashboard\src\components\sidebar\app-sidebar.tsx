import { Box, VStack, Image, Text, Button } from '@chakra-ui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FiHome, FiSettings, FiLogOut } from 'react-icons/fi';
import FlableLogo from '../../assets/icons/icon.png';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';

const AppSidebar = () => {
   const navigate = useNavigate();
   const location = useLocation();

   const userDetails = LocalStorageService.getItem(Keys.FlableUserDetails) as AuthUser;

   const handleLogout = () => {
      LocalStorageService.clearAll();
      navigate('/auth/login');
   };

   const menuItems = [
      {
         icon: FiHome,
         label: 'Dashboard',
         path: '/',
      },
      {
         icon: FiSettings,
         label: 'Settings',
         path: '/settings',
      },
   ];

   return (
      <Box
         w='250px'
         h='100vh'
         bg='white'
         borderRight='1px'
         borderColor='gray.200'
         p={4}
      >
         <VStack spacing={6} align='stretch' h='full'>
            {/* Logo */}
            <Box textAlign='center' py={4}>
               <Image src={FlableLogo} alt='Logo' boxSize='40px' mx='auto' mb={2} />
               <Text fontSize='lg' fontWeight='bold' color='blue.600'>
                  Track Dashboard
               </Text>
            </Box>

            {/* User Info */}
            <Box p={3} bg='gray.50' borderRadius='md'>
               <Text fontSize='sm' fontWeight='medium' noOfLines={1}>
                  {userDetails?.fullName}
               </Text>
               <Text fontSize='xs' color='gray.600' noOfLines={1}>
                  {userDetails?.company_name}
               </Text>
            </Box>

            {/* Navigation */}
            <VStack spacing={2} align='stretch' flex={1}>
               {menuItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = location.pathname === item.path;
                  
                  return (
                     <Button
                        key={item.path}
                        leftIcon={<Icon />}
                        variant={isActive ? 'solid' : 'ghost'}
                        colorScheme={isActive ? 'blue' : 'gray'}
                        justifyContent='flex-start'
                        onClick={() => navigate(item.path)}
                        size='md'
                     >
                        {item.label}
                     </Button>
                  );
               })}
            </VStack>

            {/* Logout */}
            <Button
               leftIcon={<FiLogOut />}
               variant='ghost'
               colorScheme='red'
               justifyContent='flex-start'
               onClick={handleLogout}
               size='md'
            >
               Logout
            </Button>
         </VStack>
      </Box>
   );
};

export default AppSidebar;
