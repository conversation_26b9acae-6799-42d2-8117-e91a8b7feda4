import { Box, Heading, Text, SimpleGrid, Card, CardBody, Stat, StatLabel, StatNumber, StatHelpText } from '@chakra-ui/react';
import { FiUsers, FiTrendingUp, FiDollarSign, FiActivity } from 'react-icons/fi';

const Dashboard = () => {
   // Placeholder data - you can replace this with real data later
   const stats = [
      {
         label: 'Total Users',
         value: '1,234',
         change: '+12%',
         icon: FiUsers,
         color: 'blue',
      },
      {
         label: 'Revenue',
         value: '$45,678',
         change: '+8%',
         icon: FiDollarSign,
         color: 'green',
      },
      {
         label: 'Growth Rate',
         value: '23.5%',
         change: '+2.3%',
         icon: FiTrendingUp,
         color: 'purple',
      },
      {
         label: 'Active Sessions',
         value: '892',
         change: '+5%',
         icon: FiActivity,
         color: 'orange',
      },
   ];

   return (
      <Box>
         <Box mb={8}>
            <Heading size='lg' mb={2}>
               Dashboard
            </Heading>
            <Text color='gray.600'>
               Welcome to your tracking dashboard. Here you can monitor your key metrics and performance.
            </Text>
         </Box>

         <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
            {stats.map((stat) => {
               const Icon = stat.icon;
               return (
                  <Card key={stat.label}>
                     <CardBody>
                        <Stat>
                           <StatLabel display='flex' alignItems='center' gap={2}>
                              <Icon color={`${stat.color}.500`} />
                              {stat.label}
                           </StatLabel>
                           <StatNumber fontSize='2xl'>{stat.value}</StatNumber>
                           <StatHelpText color={`${stat.color}.500`}>
                              {stat.change} from last month
                           </StatHelpText>
                        </Stat>
                     </CardBody>
                  </Card>
               );
            })}
         </SimpleGrid>

         <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
            <Card>
               <CardBody>
                  <Heading size='md' mb={4}>
                     Recent Activity
                  </Heading>
                  <Text color='gray.600'>
                     This section will show your recent tracking activities and events.
                     You can implement your specific tracking logic here.
                  </Text>
               </CardBody>
            </Card>

            <Card>
               <CardBody>
                  <Heading size='md' mb={4}>
                     Analytics Overview
                  </Heading>
                  <Text color='gray.600'>
                     This section will display analytics charts and graphs.
                     You can integrate your preferred charting library here.
                  </Text>
               </CardBody>
            </Card>
         </SimpleGrid>
      </Box>
   );
};

export default Dashboard;
