import { Navigate, Outlet } from 'react-router-dom';
import { Box, Flex } from '@chakra-ui/react';
import { LocalStorageService, Keys } from '../utils/local-storage';
import AppSidebar from '../components/sidebar/app-sidebar';
import AppHeader from '../components/header/app-header';

const AppLayout = () => {
   const token = LocalStorageService.getItem(Keys.Token);
   const authUser = LocalStorageService.getItem(Keys.FlableUserDetails);

   if (!token || !authUser) {
      return <Navigate to='/auth/login' replace />;
   }

   return (
      <Flex h='100vh'>
         <AppSidebar />
         <Flex direction='column' flex={1} overflow='hidden'>
            <AppHeader />
            <Box flex={1} overflow='auto' p={6}>
               <Outlet />
            </Box>
         </Flex>
      </Flex>
   );
};

export default AppLayout;
