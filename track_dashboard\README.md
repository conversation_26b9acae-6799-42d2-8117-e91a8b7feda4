# Track Dashboard

A React-based tracking dashboard application with authentication flow, built using the same architecture as the new_dashboard but focused only on dashboard functionality.

## Features

- **Complete Authentication Flow**
  - User login and registration
  - Email verification with OTP
  - Profile selection for multi-tenant users
  - Protected routes

- **Dashboard Interface**
  - Clean, modern UI using Chakra UI
  - Responsive sidebar navigation
  - Basic dashboard with placeholder metrics
  - Settings page for user management

- **Technical Stack**
  - React 18 with TypeScript
  - Vite for build tooling
  - Chakra UI for components
  - Redux Toolkit for state management
  - React Query for API management
  - React Router for navigation

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository and navigate to the track_dashboard directory
2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   - Copy `config/env/.env.example` to `config/env/.env.dev`
   - Update the `VITE_BE_API` URL to point to your backend API

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:3001`

## Project Structure

```
track_dashboard/
├── src/
│   ├── api/                 # API configuration and services
│   ├── components/          # Reusable UI components
│   ├── hooks/              # Custom React hooks
│   ├── layouts/            # Layout components (Auth, App)
│   ├── pages/              # Page components
│   │   ├── auth/           # Authentication pages
│   │   ├── dashboard/      # Dashboard pages
│   │   └── settings/       # Settings pages
│   ├── store/              # Redux store and reducers
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Utility functions and constants
├── config/env/             # Environment configuration files
└── public/                 # Static assets
```

## Authentication Flow

1. **Login/Register**: Users can create accounts or sign in
2. **Email Verification**: OTP-based email verification
3. **Profile Selection**: For users with multiple profiles/organizations
4. **Dashboard Access**: Protected dashboard with user-specific content

## Available Scripts

- `npm run dev` - Start development server
- `npm run build:dev` - Build for development
- `npm run build:prod` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## API Integration

The application expects a backend API with the following endpoints:

- `POST /auth1/login` - User login
- `POST /auth1/register` - User registration
- `POST /auth1/verify-email` - Email verification
- `POST /auth1/send-otp` - Send OTP
- `GET /auth1/user-details` - Fetch user profiles

## Customization

This is a skeleton application with basic dashboard functionality. You can:

1. **Add Custom Dashboard Logic**: Replace placeholder metrics in `src/pages/dashboard/dashboard.tsx`
2. **Implement Real API Calls**: Update API services in `src/api/service/`
3. **Add New Pages**: Create new pages and add them to the router
4. **Customize Styling**: Modify Chakra UI theme or add custom CSS
5. **Add Charts/Analytics**: Integrate charting libraries for data visualization

## Environment Configuration

The application uses Vite's environment variable system. Create environment files in `config/env/`:

- `.env.dev` - Development environment
- `.env.prod` - Production environment
- `.env.stage` - Staging environment

## Deployment

1. Build the application:
   ```bash
   npm run build:prod
   ```

2. The built files will be in the `dist/` directory
3. Serve the static files using any web server

## License

This project is private and proprietary.
