import { createBrowserRouter, Navigate } from 'react-router-dom';
import AppLayout from './layouts/app-layout';
import AuthLayout from './layouts/auth-layout';
import Login from './pages/auth/login';
import Register from './pages/auth/register';
import EmailVerification from './pages/auth/email-verification';
import ChooseProfilePage from './pages/auth/choose-profile';
import Dashboard from './pages/dashboard/dashboard';
import Settings from './pages/settings/settings';

export const router = createBrowserRouter([
   {
      path: '/',
      element: <AppLayout />,
      children: [
         {
            index: true,
            element: <Dashboard />,
         },
         {
            path: 'settings',
            element: <Settings />,
         },
      ],
   },
   {
      path: 'auth',
      element: <AuthLayout />,
      children: [
         {
            path: 'login',
            element: <Login />,
         },
         {
            path: 'register',
            element: <Register />,
         },
         {
            path: 'email-verification',
            element: <EmailVerification />,
         },
         {
            path: 'choose-profile',
            element: <ChooseProfilePage />,
         },
      ],
   },
   {
      path: '*',
      element: <Navigate to='/' replace />,
   },
]);
