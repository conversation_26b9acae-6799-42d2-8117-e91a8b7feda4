import dashboardApiAgent from "../../agent";
import { PromiseAxios } from "../common";

export interface DefaultResponse {
  status: "Success" | "Failure";
  message: string;
}
interface LoginPayload {
  email_address: string;
  password: string;
}

interface RegisterPayload {
  email_address: string;
  password: string;
  full_name: string;
  company_name: string;
  company_url: string;
  organization_type: string;
}

interface VerifyEmailPayload {
  email_address: string;
  otp: string;
}

interface SendOtpPayload {
  email_address: string;
}

interface IsEmailVerifiedPayload {
  email_address: string;
}

interface ResetPasswordPayload {
  email_address: string;
  password: string;
  otp: string;
}

interface LoginResponse {
  message: string;
  email_address: string;
  refreshToken?: string;
  register_progress?: string;
}

interface RegisterResponse {
  message: string;
  email_address: string;
}

interface VerifyEmailResponse {
  message: string;
}

interface SendOtpResponse {
  message: string;
}

interface IsEmailVerifiedResponse {
  is_verified: boolean;
}

interface ResetPasswordResponse {
  message: string;
}

interface AllUserDetails {
  email_address: string;
  client_id: string;
  company_name: string;
  company_url: string;
  organization_type: string;
  user_role: string;
  full_name: string;
  user_id: string;

  is_active: "Y" | "N";
}

interface UserDetailsResponse {
  details: AllUserDetails[];
}

export interface GetAllProfilesResponse extends DefaultResponse {
  details: AllUserDetails[];
}
interface Endpoints {
  register: (payload: RegisterPayload) => PromiseAxios<RegisterResponse>;
  login: (payload: LoginPayload) => PromiseAxios<LoginResponse>;
  sendOtp: (payload: SendOtpPayload) => PromiseAxios<SendOtpResponse>;
  verifyEmail: (
    payload: VerifyEmailPayload
  ) => PromiseAxios<VerifyEmailResponse>;
  isEmailVerified: (
    payload: IsEmailVerifiedPayload
  ) => PromiseAxios<IsEmailVerifiedResponse>;
  resetPassword: (
    payload: ResetPasswordPayload
  ) => PromiseAxios<ResetPasswordResponse>;
  fetchUserDetails: (payload: {
    email_address: string;
  }) => PromiseAxios<GetAllProfilesResponse>;
}

const authEndpoints: Endpoints = {
  register: (payload) => dashboardApiAgent.post("/auth1/register", payload),
  login: (payload) => dashboardApiAgent.post("/auth1/login", payload),
  sendOtp: (payload) => dashboardApiAgent.post("/auth1/send-otp", payload),
  verifyEmail: (payload) =>
    dashboardApiAgent.post("/auth1/verify-email", payload),
  isEmailVerified: (payload) =>
    dashboardApiAgent.get("/auth1/is-email-verified", {
      params: { email_address: payload.email_address },
    }),
  resetPassword: (payload) =>
    dashboardApiAgent.post("/auth1/reset-password", payload),
  fetchUserDetails: (payload) =>
    dashboardApiAgent.get("/user/profiles", {
      params: { email_address: payload.email_address },
    }),
};

export default authEndpoints;
export type {
  LoginPayload,
  RegisterPayload,
  AllUserDetails,
  UserDetailsResponse,
  LoginResponse,
  RegisterResponse,
};
