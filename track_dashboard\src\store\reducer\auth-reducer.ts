import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AuthUser } from '../../types/auth';

interface AuthState {
   user: AuthUser | null;
   isAuthenticated: boolean;
   token: string | null;
}

const initialState: AuthState = {
   user: null,
   isAuthenticated: false,
   token: null,
};

const authSlice = createSlice({
   name: 'auth',
   initialState,
   reducers: {
      setUser: (state, action: PayloadAction<AuthUser>) => {
         state.user = action.payload;
         state.isAuthenticated = true;
      },
      setToken: (state, action: PayloadAction<string>) => {
         state.token = action.payload;
      },
      clearAuth: (state) => {
         state.user = null;
         state.isAuthenticated = false;
         state.token = null;
      },
   },
});

export const { setUser, setToken, clearAuth } = authSlice.actions;
export default authSlice.reducer;
