import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
   Box,
   Button,
   FormControl,
   Input,
   Image,
   useToast,
   FormErrorMessage,
   Text,
   FormLabel,
   useColorModeValue,
   Stack,
   Heading,
   Link,
} from '@chakra-ui/react';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { LocalStorageService, Keys } from '../../utils/local-storage';

import ICON from '../../assets/icons/icon.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';

function EmailVerification() {
   const [otp, setOtp] = useState('');
   const [error, setError] = useState('');
   const navigate = useNavigate();
   const toast = useToast();

   const email_address = LocalStorageService.getItem(Keys.UserName) as string;

   const { isPending, mutate } = useApiMutation({
      queryKey: [keys.verifyEmail],
      mutationFn: authEndpoints.verifyEmail,
      onSuccessHandler: (response) => {
         toast({
            title: 'Success',
            description: response.message,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
         navigate('/auth/login');
      },
      onError: (msg) => {
         toast({
            title: 'Verification Failed',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const { isPending: isResending, mutate: resendOtp } = useApiMutation({
      queryKey: [keys.sendOtp],
      mutationFn: authEndpoints.sendOtp,
      onSuccessHandler: (response) => {
         toast({
            title: 'Success',
            description: 'OTP sent successfully',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: (msg) => {
         toast({
            title: 'Failed to send OTP',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateOtp = (value: string) => {
      if (!value.trim()) {
         setError('OTP is required');
         return false;
      }
      if (value.length !== 6) {
         setError('OTP must be 6 digits');
         return false;
      }
      setError('');
      return true;
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value.replace(/\D/g, '').slice(0, 6);
      setOtp(value);
      validateOtp(value);
   };

   const handleSubmit = () => {
      if (!validateOtp(otp)) {
         return;
      }
      mutate({ email_address, otp });
   };

   const handleResendOtp = () => {
      resendOtp({ email_address });
   };

   const handleBackToLogin = () => {
      navigate('/auth/login');
   };

   return (
      <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
         <Stack align='center'>
            <Image src={ICON} alt='Logo' boxSize='60px' />
            <Heading fontSize='4xl' textAlign='center'>
               Verify Email
            </Heading>
            <Text fontSize='lg' color='gray.600' textAlign='center'>
               We've sent a verification code to {email_address}
            </Text>
         </Stack>
         <Box
            rounded='lg'
            bg={useColorModeValue('white', 'gray.700')}
            boxShadow='lg'
            p={8}
         >
            <Stack spacing={4}>
               <FormControl isInvalid={!!error}>
                  <FormLabel>Verification Code</FormLabel>
                  <Input
                     type='text'
                     value={otp}
                     onChange={handleChange}
                     placeholder='Enter 6-digit code'
                     textAlign='center'
                     fontSize='xl'
                     letterSpacing='0.5em'
                  />
                  <FormErrorMessage>{error}</FormErrorMessage>
               </FormControl>
               
               <Stack spacing={10} pt={2}>
                  <Button
                     loadingText='Verifying...'
                     size='lg'
                     bg='blue.400'
                     color='white'
                     _hover={{
                        bg: 'blue.500',
                     }}
                     onClick={handleSubmit}
                     isLoading={isPending}
                  >
                     Verify Email
                  </Button>
               </Stack>
               
               <Stack pt={6}>
                  <Text align='center'>
                     Didn't receive the code?{' '}
                     <Link 
                        color='blue.400' 
                        onClick={handleResendOtp}
                        isDisabled={isResending}
                     >
                        {isResending ? 'Sending...' : 'Resend'}
                     </Link>
                  </Text>
                  <Text align='center'>
                     <Link color='blue.400' onClick={handleBackToLogin}>
                        Back to Login
                     </Link>
                  </Text>
               </Stack>
            </Stack>
         </Box>
      </Stack>
   );
}

export default EmailVerification;
