import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import {
   Box,
   Button,
   FormControl,
   Input,
   Image,
   useToast,
   FormErrorMessage,
   InputGroup,
   InputRightElement,
   Text,
   FormLabel,
   useColorModeValue,
   Stack,
   Heading,
   Link,
   Select,
} from '@chakra-ui/react';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { registerStrings } from '../../utils/strings/register-strings';
import { LocalStorageService, Keys } from '../../utils/local-storage';

import ICON from '../../assets/icons/icon.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';
import { regex } from '../../utils/strings/auth-strings';

function Register() {
   const [showPassword, setShowPassword] = useState(false);
   const [form, setForm] = useState({
      email_address: '',
      password: '',
      full_name: '',
      company_name: '',
      company_url: '',
      organization_type: '',
   });
   const [error, setError] = useState({
      email_address: '',
      password: '',
      full_name: '',
      company_name: '',
      company_url: '',
      organization_type: '',
   });

   const toast = useToast();
   const navigate = useNavigate();

   const { mutate, isPending } = useApiMutation({
      queryKey: [keys.register],
      mutationFn: authEndpoints.register,
      onSuccessHandler: (response) => {
         LocalStorageService.setItem(Keys.UserName, response.email_address);
         navigate('/auth/email-verification');

         toast({
            title: 'Success',
            description: registerStrings.mailSent,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: (message) => {
         toast({
            title: registerStrings.registerFailed,
            description: message,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateFields = (name: string, value: string) => {
      let errorMessage = '';

      switch (name) {
         case 'email_address':
            if (!value.trim()) {
               errorMessage = 'Email is required';
            } else if (!regex.email.test(value)) {
               errorMessage = 'Please enter a valid email address';
            }
            break;
         case 'password':
            if (!value.trim()) {
               errorMessage = 'Password is required';
            } else if (!regex.password.test(value)) {
               errorMessage = 'Password must be at least 8 characters';
            }
            break;
         case 'full_name':
            if (!value.trim()) {
               errorMessage = registerStrings.fullNameRequired;
            }
            break;
         case 'company_name':
            if (!value.trim()) {
               errorMessage = registerStrings.companyNameRequired;
            }
            break;
         case 'company_url':
            if (!value.trim()) {
               errorMessage = registerStrings.companyUrlRequired;
            }
            break;
         case 'organization_type':
            if (!value.trim()) {
               errorMessage = registerStrings.organizationTypeRequired;
            }
            break;
         default:
            break;
      }

      setError((prevError) => ({
         ...prevError,
         [name]: errorMessage,
      }));

      return errorMessage === '';
   };

   const validateForm = () => {
      const isEmailValid = validateFields('email_address', form.email_address);
      const isPasswordValid = validateFields('password', form.password);
      const isFullNameValid = validateFields('full_name', form.full_name);
      const isCompanyNameValid = validateFields('company_name', form.company_name);
      const isCompanyUrlValid = validateFields('company_url', form.company_url);
      const isOrgTypeValid = validateFields('organization_type', form.organization_type);
      
      return isEmailValid && isPasswordValid && isFullNameValid && 
             isCompanyNameValid && isCompanyUrlValid && isOrgTypeValid;
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      const { name, value } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: value,
      }));
      validateFields(name, value);
   };

   const handleNavigateToLogin = () => {
      navigate('/auth/login');
   };

   const handleSubmit = () => {
      if (!validateForm()) {
         return;
      }
      mutate(form);
   };

   return (
      <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
         <Stack align='center'>
            <Image src={ICON} alt='Logo' boxSize='60px' />
            <Heading fontSize='4xl' textAlign='center'>
               {registerStrings.signUp}
            </Heading>
         </Stack>
         <Box
            rounded='lg'
            bg={useColorModeValue('white', 'gray.700')}
            boxShadow='lg'
            p={8}
         >
            <Stack spacing={4}>
               <FormControl isInvalid={!!error.full_name}>
                  <FormLabel>{registerStrings.fullName}</FormLabel>
                  <Input
                     name='full_name'
                     type='text'
                     value={form.full_name}
                     onChange={handleChange}
                     placeholder='Enter your full name'
                  />
                  <FormErrorMessage>{error.full_name}</FormErrorMessage>
               </FormControl>

               <FormControl isInvalid={!!error.email_address}>
                  <FormLabel>Email</FormLabel>
                  <Input
                     name='email_address'
                     type='email'
                     value={form.email_address}
                     onChange={handleChange}
                     placeholder='Enter your email'
                  />
                  <FormErrorMessage>{error.email_address}</FormErrorMessage>
               </FormControl>
               
               <FormControl isInvalid={!!error.password}>
                  <FormLabel>Password</FormLabel>
                  <InputGroup>
                     <Input
                        name='password'
                        type={showPassword ? 'text' : 'password'}
                        value={form.password}
                        onChange={handleChange}
                        placeholder='Enter your password'
                     />
                     <InputRightElement>
                        <Button
                           variant='ghost'
                           onClick={() => setShowPassword(!showPassword)}
                        >
                           {showPassword ? <FaEyeSlash /> : <FaEye />}
                        </Button>
                     </InputRightElement>
                  </InputGroup>
                  <FormErrorMessage>{error.password}</FormErrorMessage>
               </FormControl>

               <FormControl isInvalid={!!error.company_name}>
                  <FormLabel>{registerStrings.companyName}</FormLabel>
                  <Input
                     name='company_name'
                     type='text'
                     value={form.company_name}
                     onChange={handleChange}
                     placeholder='Enter your company name'
                  />
                  <FormErrorMessage>{error.company_name}</FormErrorMessage>
               </FormControl>

               <FormControl isInvalid={!!error.company_url}>
                  <FormLabel>{registerStrings.companyUrl}</FormLabel>
                  <Input
                     name='company_url'
                     type='url'
                     value={form.company_url}
                     onChange={handleChange}
                     placeholder='Enter your company URL'
                  />
                  <FormErrorMessage>{error.company_url}</FormErrorMessage>
               </FormControl>

               <FormControl isInvalid={!!error.organization_type}>
                  <FormLabel>{registerStrings.organizationType}</FormLabel>
                  <Select
                     name='organization_type'
                     value={form.organization_type}
                     onChange={handleChange}
                     placeholder='Select organization type'
                  >
                     <option value='Individual Business'>Individual Business</option>
                     <option value='Marketing Agency'>Marketing Agency</option>
                  </Select>
                  <FormErrorMessage>{error.organization_type}</FormErrorMessage>
               </FormControl>
               
               <Stack spacing={10} pt={2}>
                  <Button
                     loadingText='Creating account...'
                     size='lg'
                     bg='blue.400'
                     color='white'
                     _hover={{
                        bg: 'blue.500',
                     }}
                     onClick={handleSubmit}
                     isLoading={isPending}
                  >
                     {registerStrings.signUp}
                  </Button>
               </Stack>
               
               <Stack pt={6}>
                  <Text align='center'>
                     {registerStrings.alreadyHaveAccount}{' '}
                     <Link color='blue.400' onClick={handleNavigateToLogin}>
                        {registerStrings.signIn}
                     </Link>
                  </Text>
               </Stack>
            </Stack>
         </Box>
      </Stack>
   );
}

export default Register;
