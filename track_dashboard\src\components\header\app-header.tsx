import { Box, Flex, Text, useColorModeValue } from '@chakra-ui/react';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';

const AppHeader = () => {
   const userDetails = LocalStorageService.getItem(Keys.FlableUserDetails) as AuthUser;

   return (
      <Box
         bg={useColorModeValue('white', 'gray.800')}
         borderBottom='1px'
         borderColor={useColorModeValue('gray.200', 'gray.700')}
         px={6}
         py={4}
      >
         <Flex justify='space-between' align='center'>
            <Text fontSize='xl' fontWeight='semibold'>
               Welcome back, {userDetails?.fullName}
            </Text>
            <Text fontSize='sm' color='gray.600'>
              {userDetails?.user_role} • {userDetails?.company_name}
            </Text>
         </Flex>
      </Box>
   );
};

export default AppHeader;
