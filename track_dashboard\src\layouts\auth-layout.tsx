import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { Box, Stack, useColorModeValue } from '@chakra-ui/react';
import { appStrings } from '../utils/strings/app-strings';
import { LocalStorageService, Keys } from '../utils/local-storage';

function AuthLayout() {
   const location = useLocation();

   const token = LocalStorageService.getItem(Keys.Token);
   const authUser = LocalStorageService.getItem(Keys.FlableUserDetails);

   const currentPath = location.pathname;

   const isSetPasswordPage = currentPath.includes('/auth/set-password');

   if (token && authUser && !isSetPasswordPage) {
      return <Navigate to={'/'} replace />;
   }

   return (
      <Box
         minH='100vh'
         bg={useColorModeValue('gray.50', 'gray.900')}
         display='flex'
         alignItems='center'
         justifyContent='center'
      >
         <Stack spacing={8} mx='auto' maxW='lg' py={12} px={6}>
            <Outlet />
         </Stack>
      </Box>
   );
}

export default AuthLayout;
