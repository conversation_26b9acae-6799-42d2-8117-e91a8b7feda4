import { Box, Heading, Text, Card, CardBody, VStack, FormControl, FormLabel, Input, Button } from '@chakra-ui/react';
import { useState } from 'react';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';

const Settings = () => {
   const userDetails = LocalStorageService.getItem(Keys.FlableUserDetails) as AuthUser;
   
   const [formData, setFormData] = useState({
      fullName: userDetails?.fullName || '',
      email: userDetails?.email || '',
      companyName: userDetails?.company_name || '',
      companyUrl: userDetails?.company_url || '',
   });

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({
         ...prev,
         [name]: value
      }));
   };

   const handleSave = () => {
      // Placeholder for save functionality
      console.log('Saving settings:', formData);
      // You can implement API call to save settings here
   };

   return (
      <Box>
         <Box mb={8}>
            <Heading size='lg' mb={2}>
               Settings
            </Heading>
            <Text color='gray.600'>
               Manage your account settings and preferences.
            </Text>
         </Box>

         <Card maxW='2xl'>
            <CardBody>
               <VStack spacing={6} align='stretch'>
                  <Heading size='md'>Account Information</Heading>
                  
                  <FormControl>
                     <FormLabel>Full Name</FormLabel>
                     <Input
                        name='fullName'
                        value={formData.fullName}
                        onChange={handleChange}
                        placeholder='Enter your full name'
                     />
                  </FormControl>

                  <FormControl>
                     <FormLabel>Email</FormLabel>
                     <Input
                        name='email'
                        value={formData.email}
                        onChange={handleChange}
                        placeholder='Enter your email'
                        type='email'
                        isReadOnly
                        bg='gray.50'
                     />
                  </FormControl>

                  <FormControl>
                     <FormLabel>Company Name</FormLabel>
                     <Input
                        name='companyName'
                        value={formData.companyName}
                        onChange={handleChange}
                        placeholder='Enter your company name'
                     />
                  </FormControl>

                  <FormControl>
                     <FormLabel>Company URL</FormLabel>
                     <Input
                        name='companyUrl'
                        value={formData.companyUrl}
                        onChange={handleChange}
                        placeholder='Enter your company URL'
                        type='url'
                     />
                  </FormControl>

                  <Box pt={4}>
                     <Button colorScheme='blue' onClick={handleSave}>
                        Save Changes
                     </Button>
                  </Box>
               </VStack>
            </CardBody>
         </Card>
      </Box>
   );
};

export default Settings;
